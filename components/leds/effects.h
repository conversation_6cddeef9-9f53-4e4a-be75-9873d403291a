#pragma once

#include "esphome.h"

namespace led_effects {

// Get current LED ring color
inline Color get_led_ring_color() {
    auto light_color = id(led_ring).current_values;
    return Color(light_color.get_red() * 255, light_color.get_green() * 255, light_color.get_blue() * 255);
}

// Get current voice assistant LEDs color
inline Color get_voice_assistant_color() {
    auto light_color = id(voice_assistant_leds).current_values;
    return Color(light_color.get_red() * 255, light_color.get_green() * 255, light_color.get_blue() * 255);
}

// Dual point rotating animation (used by Waiting and Listening effects)
inline void dual_point_rotation_effect(AddressableLight &it, Color color, int animation_index) {
    for (uint8_t i = 0; i < 12; i++) {
        if (i == animation_index % 12) {
            it[i] = color;
        } else if (i == (animation_index + 11) % 12) {
            it[i] = color * 192;
        } else if (i == (animation_index + 10) % 12) {
            it[i] = color * 128;
        } else if (i == (animation_index + 6) % 12) {
            it[i] = color;
        } else if (i == (animation_index + 5) % 12) {
            it[i] = color * 192;
        } else if (i == (animation_index + 4) % 12) {
            it[i] = color * 128;
        } else {
            it[i] = Color::BLACK;
        }
    }
}

// Reverse dual point rotation (used by Replying effect)
inline void reverse_dual_point_rotation_effect(AddressableLight &it, Color color, int animation_index) {
    for (uint8_t i = 0; i < 12; i++) {
        if (i == animation_index % 12) {
            it[i] = color;
        } else if (i == (animation_index + 1) % 12) {
            it[i] = color * 192;
        } else if (i == (animation_index + 2) % 12) {
            it[i] = color * 128;
        } else if (i == (animation_index + 6) % 12) {
            it[i] = color;
        } else if (i == (animation_index + 7) % 12) {
            it[i] = color * 192;
        } else if (i == (animation_index + 8) % 12) {
            it[i] = color * 128;
        } else {
            it[i] = Color::BLACK;
        }
    }
}

// Breathing effect with dual points (used by Thinking effect)
inline void breathing_dual_point_effect(AddressableLight &it, Color color, int animation_index,
                                       uint8_t brightness_step, uint8_t brightness_step_number) {
    for (uint8_t i = 0; i < 12; i++) {
        if (i == animation_index % 12 || i == (animation_index + 6) % 12) {
            it[i] = color * uint8_t(255/brightness_step_number*(brightness_step_number-brightness_step));
        } else {
            it[i] = Color::BLACK;
        }
    }
}

// Solid color fill (used by Center Button Touched effect)
inline void solid_fill_effect(AddressableLight &it, Color color) {
    for (uint8_t i = 0; i < 12; i++) {
        it[i] = color;
    }
}

// Breathing effect with all LEDs (used by Error and Timer Ring effects)
inline void breathing_all_leds_effect(AddressableLight &it, Color color,
                                     uint8_t brightness_step, uint8_t brightness_step_number) {
    for (uint8_t i = 0; i < 12; i++) {
        it[i] = color * uint8_t(255/brightness_step_number*(brightness_step_number-brightness_step));
    }
}

// Progressive fill effect (used by Tick and Factory Reset effects)
inline void progressive_fill_effect(AddressableLight &it, Color color, uint8_t index, bool fill_mode = false) {
    for (uint8_t i = 0; i < 12; i++) {
        if (fill_mode) {
            // Fill mode: fill from start to index
            if (i <= index) {
                it[i] = color;
            } else {
                it[i] = Color::BLACK;
            }
        } else {
            // Clear mode: clear from start to index
            if (i <= index) {
                it[i] = Color::BLACK;
            } else {
                it[i] = color;
            }
        }
    }
}

// Brightness step management for breathing effects
struct BreathingState {
    uint8_t brightness_step = 0;
    bool brightness_decreasing = true;
    uint8_t brightness_step_number = 10;

    void reset() {
        brightness_step = 0;
        brightness_decreasing = true;
    }

    void update() {
        if (brightness_decreasing) {
            brightness_step++;
        } else {
            brightness_step--;
        }

        if (brightness_step == 0 || brightness_step == brightness_step_number) {
            brightness_decreasing = !brightness_decreasing;
        }
    }
};

} // namespace led_effects
