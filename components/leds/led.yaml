esphome:
  includes:
    - effects.h

globals:
  - id: global_led_animation_index
    type: int
    restore_value: no
    initial_value: '0'

#TODO: Mute or silent, volume display, voicekit startup failed, Timer Tick
light:
  - platform: esp32_rmt_led_strip
    id: leds_internal
    internal: true
    pin: GPIO38
    chipset: WS2812
    num_leds: 12
    max_refresh_rate: 15ms
    rgb_order: GRB
    rmt_symbols: 192
    default_transition_length: 0ms

  - platform: partition
    id: led_ring
    name: 'LED Ring'
    entity_category: config
    icon: 'mdi:circle-outline'
    default_transition_length: 0ms
    restore_mode: RESTORE_DEFAULT_OFF
    initial_state:
      color_mode: rgb
      brightness: 66%
      red: 9.4%
      green: 73.3%
      blue: 94.9%
    segments:
      - id: leds_internal
        from: 0
        to: 6
      - id: leds_internal
        from: 7
        to: 11

  - platform: partition
    id: voice_assistant_leds
    internal: true
    default_transition_length: 0ms
    segments:
      - id: leds_internal
        from: 0
        to: 6
      - id: leds_internal
        from: 7
        to: 11
    effects:
      - addressable_lambda:
          name: 'Waiting for Command'
          update_interval: 100ms
          lambda: |-
            Color color = led_effects::get_led_ring_color();
            led_effects::dual_point_rotation_effect(it, color, id(global_led_animation_index));
            id(global_led_animation_index) = (id(global_led_animation_index) + 1) % 12;

      - addressable_lambda:
          name: 'Listening For Command'
          update_interval: 50ms
          lambda: |-
            Color color = led_effects::get_led_ring_color();
            led_effects::dual_point_rotation_effect(it, color, id(global_led_animation_index));
            id(global_led_animation_index) = (id(global_led_animation_index) + 1) % 12;

      - addressable_lambda:
          name: 'Thinking'
          update_interval: 10ms
          lambda: |-
            static led_effects::BreathingState breathing_state;

            if (initial_run) {
              breathing_state.reset();
            }

            Color color = led_effects::get_led_ring_color();
            led_effects::breathing_dual_point_effect(it, color, id(global_led_animation_index),
                                                   breathing_state.brightness_step, breathing_state.brightness_step_number);
            breathing_state.update();

      - addressable_lambda:
          name: 'Replying'
          update_interval: 50ms
          lambda: |-
            id(global_led_animation_index) = (12 + id(global_led_animation_index) - 1) % 12;
            Color color = led_effects::get_led_ring_color();
            led_effects::reverse_dual_point_rotation_effect(it, color, id(global_led_animation_index));

      - addressable_lambda:
          name: 'Center Button Touched'
          update_interval: 16ms
          lambda: |-
            if (initial_run) {
              auto led_ring_cv = id(led_ring).current_values;
              auto va_leds_call = id(voice_assistant_leds).make_call();

              va_leds_call.from_light_color_values(led_ring_cv);
              va_leds_call.set_brightness( min ( max( id(led_ring).current_values.get_brightness() , 0.2f ) + 0.1f , 1.0f ) );
              va_leds_call.set_state(true);
              va_leds_call.perform();
            }

            Color color = led_effects::get_voice_assistant_color();
            led_effects::solid_fill_effect(it, color);

      - addressable_twinkle:
          name: 'Twinkle'
          twinkle_probability: 50%

      - addressable_lambda:
          name: 'Error'
          update_interval: 10ms
          lambda: |-
            static led_effects::BreathingState breathing_state;

            if (initial_run) {
              breathing_state.reset();
            }

            Color error_color(255, 0, 0);
            led_effects::breathing_all_leds_effect(it, error_color, breathing_state.brightness_step, breathing_state.brightness_step_number);
            breathing_state.update();

      - addressable_lambda:
          name: 'Timer Ring'
          update_interval: 10ms
          lambda: |-
            static led_effects::BreathingState breathing_state;

            if (initial_run) {
              breathing_state.reset();
            }

            Color color = led_effects::get_led_ring_color();
            led_effects::breathing_all_leds_effect(it, color, breathing_state.brightness_step, breathing_state.brightness_step_number);
            breathing_state.update();

      - addressable_rainbow:
          name: 'Rainbow'
          width: 12

      - addressable_lambda:
          name: 'Tick'
          update_interval: 333ms
          lambda: |-
            static uint8_t index = 0;
            Color color(255, 0, 0);

            if (initial_run) {
              index = 0;
            }

            led_effects::progressive_fill_effect(it, color, index, false);
            index = (index + 1) % 12;

      - addressable_lambda:
          name: 'Factory Reset Coming Up'
          update_interval: 1s
          lambda: |-
            static uint8_t index = 0;
            Color color(255, 0, 0);

            if (initial_run) {
              index = 0;
            }

            led_effects::progressive_fill_effect(it, color, index, true);
            index = (index + 1) % 12;
