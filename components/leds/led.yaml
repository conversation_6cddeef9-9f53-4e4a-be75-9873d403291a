esphome:
  includes:
    - effects.h

globals:
  - id: global_led_animation_index
    type: int
    restore_value: no
    initial_value: '0'

#TODO: Mute or silent, volume display, voicekit startup failed, Timer Tick
light:
  - platform: esp32_rmt_led_strip
    id: leds_internal
    internal: true
    pin: GPIO38
    chipset: WS2812
    num_leds: 12
    max_refresh_rate: 15ms
    rgb_order: GRB
    rmt_symbols: 192
    default_transition_length: 0ms

  - platform: partition
    id: led_ring
    name: 'LED Ring'
    entity_category: config
    icon: 'mdi:circle-outline'
    default_transition_length: 0ms
    restore_mode: RESTORE_DEFAULT_OFF
    initial_state:
      color_mode: rgb
      brightness: 66%
      red: 9.4%
      green: 73.3%
      blue: 94.9%
    segments:
      - id: leds_internal
        from: 0
        to: 6
      - id: leds_internal
        from: 7
        to: 11

  - platform: partition
    id: voice_assistant_leds
    internal: true
    default_transition_length: 0ms
    segments:
      - id: leds_internal
        from: 0
        to: 6
      - id: leds_internal
        from: 7
        to: 11
    effects:
      - addressable_lambda:
          name: 'Waiting for Command'
          update_interval: 100ms
          lambda: |-
            auto light_color = id(led_ring).current_values;
            Color color(light_color.get_red() * 255, light_color.get_green() * 255, light_color.get_blue() * 255);

            for (uint8_t i = 0; i < 12; i++) {
              if (i == id(global_led_animation_index) % 12) {
                it[i] = color;
              } else if (i == (id(global_led_animation_index) + 11) % 12) {
                it[i] = color * 192;
              } else if (i == (id(global_led_animation_index) + 10) % 12) {
                it[i] = color * 128;
              } else if (i == (id(global_led_animation_index) + 6) % 12) {
                it[i] = color;
              } else if (i == (id(global_led_animation_index) + 5) % 12) {
                it[i] = color * 192;
              } else if (i == (id(global_led_animation_index) + 4) % 12) {
                it[i] = color * 128;
              } else {
                it[i] = Color::BLACK;
              }
            }

            id(global_led_animation_index) = (id(global_led_animation_index) + 1) % 12;

      - addressable_lambda:
          name: 'Listening For Command'
          update_interval: 50ms
          lambda: |-
            auto light_color = id(led_ring).current_values;
            Color color(light_color.get_red() * 255, light_color.get_green() * 255, light_color.get_blue() * 255);

            for (uint8_t i = 0; i < 12; i++) {
              if (i == id(global_led_animation_index) % 12) {
                it[i] = color;
              } else if (i == (id(global_led_animation_index) + 11) % 12) {
                it[i] = color * 192;
              } else if (i == (id(global_led_animation_index) + 10) % 12) {
                it[i] = color * 128;
              } else if (i == (id(global_led_animation_index) + 6) % 12) {
                it[i] = color;
              } else if (i == (id(global_led_animation_index) + 5) % 12) {
                it[i] = color * 192;
              } else if (i == (id(global_led_animation_index) + 4) % 12) {
                it[i] = color * 128;
              } else {
                it[i] = Color::BLACK;
              }
            }

            id(global_led_animation_index) = (id(global_led_animation_index) + 1) % 12;

      - addressable_lambda:
          name: 'Thinking'
          update_interval: 10ms
          lambda: |-
            static uint8_t brightness_step = 0;
            static bool brightness_decreasing = true;
            static uint8_t brightness_step_number = 10;

            if (initial_run) {
              brightness_step = 0;
              brightness_decreasing = true;
            }

            auto light_color = id(led_ring).current_values;
            Color color(light_color.get_red() * 255, light_color.get_green() * 255, light_color.get_blue() * 255);

            for (uint8_t i = 0; i < 12; i++) {
              if (i == id(global_led_animation_index) % 12) {
                it[i] = color * uint8_t(255/brightness_step_number*(brightness_step_number-brightness_step));
              } else if (i == (id(global_led_animation_index) + 6) % 12) {
                it[i] = color * uint8_t(255/brightness_step_number*(brightness_step_number-brightness_step));
              } else {
                it[i] = Color::BLACK;
              }
            }

            if (brightness_decreasing) {
              brightness_step++;
            } else {
              brightness_step--;
            }

            if (brightness_step == 0 || brightness_step == brightness_step_number) {
              brightness_decreasing = !brightness_decreasing;
            }

      - addressable_lambda:
          name: 'Replying'
          update_interval: 50ms
          lambda: |-
            id(global_led_animation_index) = (12 + id(global_led_animation_index) - 1) % 12;
            auto light_color = id(led_ring).current_values;
            Color color(light_color.get_red() * 255, light_color.get_green() * 255, light_color.get_blue() * 255);

            for (uint8_t i = 0; i < 12; i++) {
              if (i == (id(global_led_animation_index)) % 12) {
                it[i] = color;
              } else if (i == ( id(global_led_animation_index) + 1) % 12) {
                it[i] = color * 192;
              } else if (i == ( id(global_led_animation_index) + 2) % 12) {
                it[i] = color * 128;
              } else if (i == ( id(global_led_animation_index) + 6) % 12) {
                it[i] = color;
              } else if (i == ( id(global_led_animation_index) + 7) % 12) {
                it[i] = color * 192;
              } else if (i == ( id(global_led_animation_index) + 8) % 12) {
                it[i] = color * 128;
              } else {
                it[i] = Color::BLACK;
              }
            }

      - addressable_lambda:
          name: 'Center Button Touched'
          update_interval: 16ms
          lambda: |-
            if (initial_run) {
              auto led_ring_cv = id(led_ring).current_values;
              auto va_leds_call = id(voice_assistant_leds).make_call();

              va_leds_call.from_light_color_values(led_ring_cv);
              va_leds_call.set_brightness( min ( max( id(led_ring).current_values.get_brightness() , 0.2f ) + 0.1f , 1.0f ) );
              va_leds_call.set_state(true);
              va_leds_call.perform();
            }

            auto light_color = id(voice_assistant_leds).current_values;
            Color color(light_color.get_red() * 255, light_color.get_green() * 255, light_color.get_blue() * 255);

            for (uint8_t i = 0; i < 12; i++) {
              it[i] = color;
            }

      - addressable_twinkle:
          name: 'Twinkle'
          twinkle_probability: 50%

      - addressable_lambda:
          name: 'Error'
          update_interval: 10ms
          lambda: |-
            static uint8_t brightness_step = 0;
            static bool brightness_decreasing = true;
            static uint8_t brightness_step_number = 10;

            if (initial_run) {
              brightness_step = 0;
              brightness_decreasing = true;
            }

            Color error_color(255, 0, 0);

            for (uint8_t i = 0; i < 12; i++) {
              it[i] = error_color * uint8_t(255/brightness_step_number*(brightness_step_number-brightness_step));
            }

            if (brightness_decreasing) {
              brightness_step++;
            } else {
              brightness_step--;
            }

            if (brightness_step == 0 || brightness_step == brightness_step_number) {
              brightness_decreasing = !brightness_decreasing;
            }

      - addressable_lambda:
          name: 'Timer Ring'
          update_interval: 10ms
          lambda: |-
            static uint8_t brightness_step = 0;
            static bool brightness_decreasing = true;
            static uint8_t brightness_step_number = 10;

            if (initial_run) {
              brightness_step = 0;
              brightness_decreasing = true;
            }

            auto light_color = id(led_ring).current_values;
            Color color(light_color.get_red() * 255, light_color.get_green() * 255, light_color.get_blue() * 255);

            for (uint8_t i = 0; i < 12; i++) {
              it[i] = color * uint8_t(255/brightness_step_number*(brightness_step_number-brightness_step));
            }

            if (brightness_decreasing) {
              brightness_step++;
            } else {
              brightness_step--;
            }

            if (brightness_step == 0 || brightness_step == brightness_step_number) {
              brightness_decreasing = !brightness_decreasing;
            }

      - addressable_rainbow:
          name: 'Rainbow'
          width: 12

      - addressable_lambda:
          name: 'Tick'
          update_interval: 333ms
          lambda: |-
            static uint8_t index = 0;
            Color color(255, 0, 0);

            if (initial_run) {
              index = 0;
            }

            for (uint8_t i = 0; i < 12; i++) {
              if (i <= index ) {
                it[i] = Color::BLACK;
              } else {
                it[i] = color;
              }
            }

            index = (index + 1) % 12;

      - addressable_lambda:
          name: 'Factory Reset Coming Up'
          update_interval: 1s
          lambda: |-
            static uint8_t index = 0;
            Color color(255, 0, 0);

            if (initial_run) {
              index = 0;
            }

            for (uint8_t i = 0; i < 12; i++) {
              if (i <= index ) {
                it[i] = color;
              } else {
                it[i] = Color::BLACK;
              }
            }

            index = (index + 1) % 12;
